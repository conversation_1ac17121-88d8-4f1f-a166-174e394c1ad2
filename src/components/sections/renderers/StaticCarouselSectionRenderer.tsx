import React from "react";
import { <PERSON>, FlatList, StyleSheet } from "react-native";
import {
	CarouselSectionRendererProps,
	SectionWithItems,
} from "../../../types/sectionTypes";
import SectionHeader from "../../common/SectionHeader";
import VideoCard from "../../common/VideoCard";
import { scale } from "../../../utils/helpers/dimensionScale.helper";
import { optimizeImageUrl } from "../../../utils/helpers/imageOptimization.helper";
import { GLOBAL_STYLES } from "../../../styles/globalStyles";
import { formatVideoDuration } from "../../../utils/helpers/formatDuration.helper";

/**
 * StaticCarouselSectionRenderer Component
 *
 * Renders a horizontal carousel of static items (videos, categories, etc.)
 * Used for sections with type section_static_carousel
 */
const StaticCarouselSectionRenderer: React.FC<
	CarouselSectionRendererProps
> = ({
	data,
	onPress,
	accessibilityLabel,
	isAuthenticated = false,
}) => {
	// Cast data to SectionWithItems to ensure TypeScript knows it has an items property
	const sectionData = data as SectionWithItems;
	// Extract items from the data - check both items and Videos properties
	const items = sectionData.items || [];

	// Don't render anything if there are no items
	if (!items || items.length === 0) {
		return null;
	}

	// Check if all items are empty placeholders
	const allItemsArePlaceholders = items.every((item: any) => {
		// Check if item has no meaningful content
		return (
			!item.name &&
			!item.title &&
			!item.poster &&
			!item.thumbnail &&
			!item.itemId &&
			!item.id
		);
	});

	// Don't render if all items are placeholders
	if (allItemsArePlaceholders) {
		return null;
	}

	// Map the items to a consistent format
	const mappedItems = items.map((item: any) => {
		// Process each item

		// Check if this is a competition item
		const isCompetition =
			item.isCompetition === true ||
			sectionData._kenticoCodename ===
				"grid_home_page___competitions" ||
			(item.pageCodename && item.pageCodename.startsWith("page_"));

		// Extract pageCodename for competition items
		let pageCodename = item.pageCodename;

		// Check if we have mobileRedirectionTarget in the image property
		if (isCompetition && item.image?.mobileRedirectionTarget) {
			// Extract the page codename from the URL format "hbtv://page_name"
			const match =
				item.image.mobileRedirectionTarget.match(/hbtv:\/\/(.*)/);
			if (match && match[1]) {
				pageCodename = match[1];
			}
		}

		// Fallback to other possible sources if mobileRedirectionTarget is not available
		if (isCompetition && !pageCodename) {
			pageCodename =
				item.redirectionTarget?.web_menu_item?.redirectionTarget ||
				item.pageCodename ||
				(item.name &&
					`page_${item.name.toLowerCase().replace(/\s+/g, "_")}`);
		}

		// Set the correct item type
		const itemType = isCompetition
			? "competition"
			: item.itemType || "video";
		const id = item.itemId || item.id || "";
		const title = item.name || item.title || "";

		// Enhanced image URL extraction for different item types
		let imageUrl = "";

		// Check if this is a competition item (they have different image structures)
		if (
			item.image &&
			(item.image.url || item.image.image || item.image.logo)
		) {
			// Competition items often have nested image structures
			imageUrl =
				item.image.url ||
				(item.image.image && item.image.image.url) ||
				(item.image.logo && item.image.logo.url) ||
				item.logo?.url ||
				"";

			// If we still don't have an image, check for mobileImage
			if (
				!imageUrl &&
				item.image.mobileImage &&
				item.image.mobileImage.url
			) {
				imageUrl = item.image.mobileImage.url;
			}
		} else {
			// Standard video/content items
			imageUrl =
				item.poster ||
				(item.thumbnail ? String(item.thumbnail) : "") ||
				item.logo?.url ||
				"";
		}

		const duration = item.duration || "";

		// Use a stable key without random values
		// Ensure the key is unique by including more information
		const key = id
			? `${itemType}-${id}`
			: `${itemType}-${title}-${Math.random()
					.toString(36)
					.substring(2, 10)}`;

		// Format metadata - put duration first, then other info
		let metadataStr = "";
		if (duration) {
			// Format duration to remove leading zeros for hours
			metadataStr = formatVideoDuration(duration);
		}

		// Build the rest of the metadata string (for debugging/logging only)
		let additionalInfo = "";
		if (item.isNew) additionalInfo += "New";
		if (item.views) {
			additionalInfo += additionalInfo
				? ` • ${item.views} views`
				: `${item.views} views`;
		}

		// Optimize the image URL to prevent memory issues
		const optimizedImageUrl = imageUrl
			? optimizeImageUrl(imageUrl, "OPTIMIZED")
			: null;

		// For competition items, make sure we include the pageCodename
		return {
			id,
			key,
			title,
			imageUrl: optimizedImageUrl || imageUrl, // Use optimized URL if available, fall back to original
			duration,
			type: itemType,
			metadata: metadataStr,
			// Include pageCodename for competition items
			pageCodename:
				pageCodename || (itemType === "competition" ? id : undefined),
			// Flag to identify competition items
			isCompetition: itemType === "competition",
		};
	});

	// Get spacing for items
	const getSpacing = () => {
		return GLOBAL_STYLES.ITEM_SPACING;
	};

	// Render the card
	const renderCard = ({ item }: { item: any }) => {
		// Create event data object to pass to the onPress handler
		const eventData = {
			name: item.title,
			poster: item.imageUrl,
			description: "",
			isLive:
				item.type === "live-event" || item.key?.startsWith("live-"),
			startDate: item.startDate,
			// Add pageCodename for competition items
			pageCodename: item.pageCodename,
			// Add isCompetition flag
			isCompetition: item.isCompetition,
		};

		// Log navigation for competition items
		if (item.type === "competition") {
			// For competition items, ensure we have a valid pageCodename
			// If the item is from the competitions section, the id might be the pageCodename
			if (
				!item.pageCodename &&
				item.id &&
				item.id.startsWith("page_")
			) {
				// If id looks like a pageCodename (starts with "page_"), use it
				eventData.pageCodename = item.id;
			}
		}

		// Check if content is paid based on products existence or assume potentially paid
		// For static carousel videos, we assume they might be paid and show lock icons accordingly
		const isPaid =
			(item.products && item.products.length > 0) ||
			(item.ItemProducts && item.ItemProducts.length > 0) ||
			item.isPaid === true ||
			(!isAuthenticated && item.type === "video"); // Show lock for non-authenticated users on videos only

		return (
			<VideoCard
				id={item.id}
				title={item.title}
				imageUrl={item.imageUrl}
				onPress={() => onPress?.(item.id, item.type, eventData)}
				metadata={item.metadata}
				isPaid={isPaid}
				isAuthenticated={isAuthenticated}
			/>
		);
	};

	// Get the section title
	const sectionTitle = sectionData.title || "";

	return (
		<View
			style={styles.container}
			accessible={true}
			accessibilityLabel={
				accessibilityLabel || `Section: ${sectionTitle || "Items"}`
			}
		>
			{/* Section header with the title */}
			<SectionHeader title={sectionTitle} />

			{/* Carousel */}
			<FlatList
				data={mappedItems}
				horizontal
				showsHorizontalScrollIndicator={false}
				keyExtractor={(item) => {
					// Ensure we always have a stable, unique key for each item
					return item.key || `${item.type || "item"}-${item.id}`;
				}}
				contentContainerStyle={styles.list}
				ItemSeparatorComponent={() => (
					<View style={{ width: getSpacing() }} />
				)}
				renderItem={renderCard}
				// Performance optimizations
				removeClippedSubviews={true}
				maxToRenderPerBatch={5}
				windowSize={5}
				initialNumToRender={5}
			/>
		</View>
	);
};

const styles = StyleSheet.create({
	container: {
		marginBottom: GLOBAL_STYLES.SECTION_MARGIN_BOTTOM,
		// No horizontal padding as it will be handled by the parent container
	},
	list: {
		paddingTop: scale(4), // Further reduced space between section title and content
		paddingBottom: scale(4), // Further reduced bottom padding
	},
});

export default React.memo(StaticCarouselSectionRenderer);
