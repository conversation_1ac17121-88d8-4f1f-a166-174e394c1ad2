import React from "react";
import { <PERSON>, FlatList, StyleSheet, Text } from "react-native";
import {
	BaseSectionRendererProps,
	SectionWithItems,
} from "../../../types/sectionTypes";
import { CarouselStaticSection } from "../../../utils/apis/generated/kentico";
import VideoCard from "../../common/VideoCard";
import { scale } from "../../../utils/helpers/dimensionScale.helper";
import { optimizeImageUrl } from "../../../utils/helpers/imageOptimization.helper";
import { formatDuration } from "../../../utils/helpers/formatDuration.helper";
import { GLOBAL_STYLES } from "../../../styles/globalStyles";

/**
 * Props for the Featured Video Section Renderer
 */
export interface FeaturedVideoSectionRendererProps
	extends BaseSectionRendererProps<CarouselStaticSection> {
	isAuthenticated?: boolean; // Authentication state for paid content overlays
}

/**
 * FeaturedVideoSectionRenderer Component
 *
 * Specialized renderer for featured video sections on competition pages
 * Displays larger video cards in a horizontal layout, similar to the "In the News" section on the homepage
 */
const FeaturedVideoSectionRenderer: React.FC<
	FeaturedVideoSectionRendererProps
> = ({
	data,
	onPress,
	accessibilityLabel,
	isAuthenticated = false,
}) => {
	// Cast data to SectionWithItems to ensure TypeScript knows it has an items property
	const sectionData = data as SectionWithItems;
	// Extract items from the data
	const items = sectionData.items || [];

	// Don't render anything if there are no items
	if (!items || items.length === 0) {
		return null;
	}

	// Calculate dimensions for featured videos - using 16:9 aspect ratio
	const FEATURED_VIDEO_WIDTH = scale(600); // Increased width
	const FEATURED_VIDEO_HEIGHT = scale(337); // Maintained 16:9 aspect ratio

	// Map the items to a consistent format
	const mappedItems = items.map((item: any, index: number) => {
		// Extract basic information
		const id =
			item.itemId || item._kenticoId || item.id || `video-${index}`;
		const title = item.name || item.title || `Video ${index + 1}`;

		// Get image URL from the nested structure
		let imageUrl = "";

		// Check for the nested image structure
		if (item.image && item.image.image && item.image.image.url) {
			imageUrl = item.image.image.url;
		} else if (item.poster) {
			imageUrl = item.poster;
		}

		// Optimize the image URL to prevent memory issues
		// Using the TV preset which has appropriate dimensions for featured videos
		// This ensures thumbnails are displayed at the proper resolution
		const optimizedImageUrl = imageUrl
			? optimizeImageUrl(imageUrl, "TV")
			: null;

		// Extract metadata if available and format duration to remove leading zeros for hours
		const duration = item.duration
			? formatDuration(item.duration)
			: "";
		const metadata = item.metadata || duration || "";

		return {
			id,
			key: `featured-video-${id}`,
			title,
			imageUrl: optimizedImageUrl || imageUrl,
			metadata,
			type: "video",
		};
	});

	// Render the video card
	const renderVideoCard = ({ item }: { item: any }) => {
		// Create event data object to pass to the onPress handler
		const eventData = {
			name: item.title,
			poster: item.imageUrl,
			description: item.description || "",
			isLive: false,
			startDate: null,
		};

		// Check if content is paid based on products existence
		// Only show lock icon for videos that actually have products (are paid)
		const isPaid =
			(item.products && item.products.length > 0) ||
			(item.ItemProducts && item.ItemProducts.length > 0) ||
			item.isPaid === true;

		return (
			<VideoCard
				id={item.id}
				title={item.title}
				imageUrl={item.imageUrl}
				onPress={(id) => onPress?.(id, item.type, eventData)}
				width={FEATURED_VIDEO_WIDTH}
				height={FEATURED_VIDEO_HEIGHT}
				metadata={item.metadata}
				isPaid={isPaid}
				isAuthenticated={isAuthenticated}
			/>
		);
	};

	// Get the section title
	const sectionTitle = sectionData.title || "";

	return (
		<View
			style={styles.container}
			accessible={true}
			accessibilityLabel={
				accessibilityLabel ||
				`Featured Videos: ${sectionTitle || "Featured"}`
			}
		>
			{/* Custom larger section header with the title */}
			<View style={styles.headerContainer}>
				<Text style={styles.headerTitle}>{sectionTitle}</Text>
			</View>

			{/* Featured Videos Carousel */}
			<FlatList
				data={mappedItems}
				horizontal
				showsHorizontalScrollIndicator={false}
				keyExtractor={(item) => {
					return item.key || `${item.type || "video"}-${item.id}`;
				}}
				contentContainerStyle={styles.list}
				ItemSeparatorComponent={() => (
					<View style={{ width: GLOBAL_STYLES.ITEM_SPACING }} />
				)}
				renderItem={renderVideoCard}
				// Performance optimizations for memory efficiency
				removeClippedSubviews={true}
				maxToRenderPerBatch={3}
				windowSize={3}
				initialNumToRender={2}
				updateCellsBatchingPeriod={50}
				onEndReachedThreshold={0.5}
			/>
		</View>
	);
};

const styles = StyleSheet.create({
	container: {
		marginBottom: GLOBAL_STYLES.SECTION_MARGIN_BOTTOM,
		// No horizontal padding as it will be handled by the parent container
	},
	list: {
		paddingVertical: scale(2), // Reduced vertical padding to minimize spacing
		// No horizontal padding as it will be handled by the parent container
	},
	headerContainer: {
		paddingVertical: scale(2), // Reduced vertical padding around section title
		marginBottom: scale(12), // Increased bottom margin after section title for better spacing
		// No horizontal padding as it will be handled by the parent container
	},
	headerTitle: {
		color: "white",
		fontSize: scale(42),
		fontWeight: "bold",
		textTransform: "uppercase",
		letterSpacing: scale(0.5),
	},
});

export default React.memo(FeaturedVideoSectionRenderer);
