import React from "react";
import { <PERSON>, FlatList, StyleSheet } from "react-native";
import {
	BaseSectionRendererProps,
	SectionWithItems,
} from "../../../types/sectionTypes";
import SectionHeader from "../../common/SectionHeader";
import VideoCard from "../../common/VideoCard";
import { scale } from "../../../utils/helpers/dimensionScale.helper";
import { optimizeImageUrl } from "../../../utils/helpers/imageOptimization.helper";
import { GLOBAL_STYLES } from "../../../styles/globalStyles";
import { formatVideoDuration } from "../../../utils/helpers/formatDuration.helper";

/**
 * Props for the Dynamic Carousel Section Renderer
 * This renderer is specifically designed for dynamic carousel sections that use Videos property
 */
export interface DynamicCarouselSectionRendererProps
	extends BaseSectionRendererProps<any> {
	isAuthenticated?: boolean; // Authentication state for paid content overlays
}

/**
 * DynamicCarouselSectionRenderer Component
 *
 * Renders a horizontal carousel of videos from dynamic sections that use the Videos property
 * instead of items property (like the "Débrief & réactions" section)
 */
const DynamicCarouselSectionRenderer: React.FC<
	DynamicCarouselSectionRendererProps
> = ({
	data,
	onPress,
	accessibilityLabel,
	isAuthenticated = false,
}) => {
	// Extract videos from the data - dynamic carousels use Videos property instead of items
	const videos = data.Videos || [];

	// Don't render anything if there are no videos
	if (!videos || videos.length === 0) {
		console.log(
			`No videos found in dynamic carousel: ${data._kenticoCodename}`
		);
		return null;
	}

	// Check if all videos are empty placeholders
	const allVideosArePlaceholders = videos.every((video: any) => {
		// Check if video has no meaningful content
		return (
			!video.name &&
			!video.title &&
			!video.poster &&
			!video.thumbnail &&
			!video.itemId &&
			!video.id
		);
	});

	// Don't render if all videos are placeholders
	if (allVideosArePlaceholders) {
		console.log(
			`All videos are placeholders in: ${data._kenticoCodename}`
		);
		return null;
	}

	// Map the videos to a consistent format
	const mappedVideos = videos.map((video: any) => {
		// Process each video
		const itemType = video.itemType || "video";
		const id = video.itemId || video.id || "";
		const title = video.name || video.title || "";
		const imageUrl =
			video.poster ||
			(video.thumbnail ? String(video.thumbnail) : "");
		const duration = video.duration || "";

		// Use a stable key without random values
		const key = `${itemType}-${id}`;

		// Format metadata - put duration first, then other info
		let metadataStr = "";
		if (duration) {
			// Format duration to remove leading zeros for hours
			metadataStr += formatVideoDuration(duration);
		}

		// Build the rest of the metadata string (for debugging/logging only)
		let additionalInfo = "";
		if (video.isNew) additionalInfo += "New";
		if (video.views) {
			additionalInfo += additionalInfo
				? ` • ${video.views} views`
				: `${video.views} views`;
		}

		// Optimize the image URL to prevent memory issues
		const optimizedImageUrl = imageUrl
			? optimizeImageUrl(imageUrl, "OPTIMIZED")
			: null;

		return {
			id,
			key,
			title,
			imageUrl: optimizedImageUrl || imageUrl, // Use optimized URL if available, fall back to original
			duration,
			type: itemType,
			metadata: metadataStr,
		};
	});

	// Get spacing for videos
	const getSpacing = () => {
		return GLOBAL_STYLES.ITEM_SPACING;
	};

	// Render the card
	const renderCard = ({ item }: { item: any }) => {
		// Create event data object to pass to the onPress handler
		const eventData = {
			name: item.title,
			poster: item.imageUrl,
			description: "",
			isLive:
				item.type === "live-event" || item.key?.startsWith("live-"),
			startDate: item.startDate,
		};

		// Check if content is paid based on products existence
		// Only show lock icon for videos that actually have products (are paid)
		const isPaid =
			(item.products && item.products.length > 0) ||
			(item.ItemProducts && item.ItemProducts.length > 0) ||
			item.isPaid === true;

		return (
			<VideoCard
				id={item.id}
				title={item.title}
				imageUrl={item.imageUrl}
				onPress={() => onPress?.(item.id, item.type, eventData)}
				metadata={item.metadata}
				isPaid={isPaid}
				isAuthenticated={isAuthenticated}
			/>
		);
	};

	// Get the section title
	const sectionTitle = data.title || data.name || "";

	return (
		<View
			style={styles.container}
			accessible={true}
			accessibilityLabel={
				accessibilityLabel || `Section: ${sectionTitle || "Videos"}`
			}
		>
			{/* Section header with the title */}
			<SectionHeader title={sectionTitle} />

			{/* Carousel */}
			<FlatList
				data={mappedVideos}
				horizontal
				showsHorizontalScrollIndicator={false}
				keyExtractor={(item) => {
					return item.key || `${item.type || "item"}-${item.id}`;
				}}
				contentContainerStyle={styles.list}
				ItemSeparatorComponent={() => (
					<View style={{ width: getSpacing() }} />
				)}
				renderItem={renderCard}
				// Performance optimizations
				removeClippedSubviews={true}
				maxToRenderPerBatch={5}
				windowSize={5}
				initialNumToRender={5}
			/>
		</View>
	);
};

const styles = StyleSheet.create({
	container: {
		marginBottom: GLOBAL_STYLES.SECTION_MARGIN_BOTTOM,
	},
	list: {
		paddingVertical: scale(16), // Increased vertical padding for better spacing
	},
});

export default React.memo(DynamicCarouselSectionRenderer);
