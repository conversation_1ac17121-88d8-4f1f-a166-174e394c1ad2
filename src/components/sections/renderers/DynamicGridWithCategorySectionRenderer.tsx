import React, { useState, useEffect, useCallback } from "react";
import {
	View,
	FlatList,
	StyleSheet,
	ActivityIndicator,
} from "react-native";
import { BaseSectionRendererProps } from "../../../types/sectionTypes";
import SectionHeader from "../../common/SectionHeader";
import VideoCard from "../../common/VideoCard";
import { scale } from "../../../utils/helpers/dimensionScale.helper";
import { kenticoAPIClient } from "../../../utils/kenticoInstance";
import { optimizeImageUrl } from "../../../utils/helpers/imageOptimization.helper";
import { formatDuration } from "../../../utils/helpers/formatDuration.helper";
import { GLOBAL_STYLES } from "../../../styles/globalStyles";

// Define a video item interface based on the actual API response structure
interface VideoItem {
	itemId: string;
	name: string; // The API returns 'name' instead of 'title'
	poster?: string; // The API returns 'poster' instead of 'imageUrl'
	duration?: string; // Duration of the video
	description?: string; // Video description
	views?: number; // Number of views
	categoryId?: string; // ID of the category this video belongs to
}

/**
 * Props for the Dynamic Grid With Category Section Renderer
 * This renderer displays videos in a horizontal row for TV platforms
 */
export interface DynamicGridWithCategorySectionRendererProps
	extends BaseSectionRendererProps<any> {
	onPress: (id: string, type: string, eventData?: any) => void;
	isAuthenticated?: boolean; // Authentication state for paid content overlays
}

/**
 * DynamicGridWithCategorySectionRenderer Component
 *
 * Renders videos in a horizontal row for TV platforms
 * Used for sections like "Twitch @LNHOFFICIEL" that have videos
 */
const DynamicGridWithCategorySectionRenderer: React.FC<
	DynamicGridWithCategorySectionRendererProps
> = ({ data, onPress, isAuthenticated = false }) => {
	// State for videos, loading state, and cursor
	const [videos, setVideos] = useState<VideoItem[]>([]);
	const [loadingMore, setLoadingMore] = useState(false);
	const [cursor, setCursor] = useState<string | null>(null);
	const [hasMore, setHasMore] = useState(true);

	// Extract section title and category ID
	const sectionTitle = data.title || data.name || "";
	const categoryId = data.categoryId || data.id;

	// Initialize videos state with initial data
	useEffect(() => {
		// Get initial videos from data
		const initialVideos = (data.Videos || []) as VideoItem[];

		// Log cursor information if available
		if (data.cursor) {
			console.log(
				`Cursor information for ${data._kenticoCodename}:`,
				data.cursor
			);
			// Set initial cursor
			setCursor(data.cursor.after || null);
			setHasMore(!!data.cursor.after);
		}

		// Set initial videos
		setVideos(initialVideos);
	}, [data]);

	// Function to load more videos
	const handleLoadMore = useCallback(async () => {
		// Skip if already loading, no more items, no cursor, or no categoryId
		if (loadingMore || !hasMore || !cursor || !categoryId) {
			console.log(
				`[DynamicGridWithCategorySectionRenderer] Skipping load more - conditions not met`
			);
			return;
		}

		console.log(
			`[DynamicGridWithCategorySectionRenderer] Loading more videos for ${data._kenticoCodename} with cursor: ${cursor}`
		);
		setLoadingMore(true);

		try {
			// Call API to get more videos
			const response = await kenticoAPIClient.ott.getCategoryVideosV2(
				categoryId,
				{
					language: "en",
					limit: 10, // Fetch 10 more videos
					after: cursor,
				}
			);

			// Process response - handle both array and object with items property
			const responseData = response.data;
			let newVideos: any[] = [];
			let newCursor: string | null = null;

			// Check if response is an array or has items property
			if (Array.isArray(responseData)) {
				newVideos = responseData as VideoItem[];
			} else if (responseData && typeof responseData === "object") {
				// Check if it has items property
				if (
					"items" in responseData &&
					Array.isArray(responseData.items)
				) {
					newVideos = responseData.items as VideoItem[];
				}

				// Check if it has cursor property
				if (
					"cursor" in responseData &&
					responseData.cursor &&
					responseData.cursor.after
				) {
					newCursor = responseData.cursor.after;
				}
			}

			// Add new videos to state if we got any
			if (newVideos.length > 0) {
				setVideos((prev) => [...prev, ...newVideos]);
			}

			// Update cursor
			setCursor(newCursor);
			setHasMore(!!newCursor);
		} catch (error) {
			console.error(
				`[DynamicGridWithCategorySectionRenderer] Error loading more videos:`,
				error
			);
		} finally {
			setLoadingMore(false);
		}
	}, [
		cursor,
		data._kenticoCodename,
		categoryId,
		hasMore,
		loadingMore,
	]);

	// Handle end reached
	const onEndReached = useCallback(() => {
		console.log(
			`[DynamicGridWithCategorySectionRenderer] End reached`
		);
		handleLoadMore();
	}, [handleLoadMore]);

	// Don't render anything if there are no videos
	if (!videos || videos.length === 0) {
		console.log(
			`No videos found in dynamic grid with category: ${data._kenticoCodename}`
		);
		return null;
	}

	// Render a video card
	const renderCard = ({ item }: { item: VideoItem }) => {
		// Format duration as metadata if available
		const metadata = item.duration
			? formatDuration(item.duration) ?? undefined
			: item.views
			? `Views: ${item.views}`
			: undefined;

		// Optimize the image URL to prevent memory issues
		const optimizedImageUrl = item.poster
			? optimizeImageUrl(item.poster, "OPTIMIZED")
			: null;

		// Create event data object to pass to the onPress handler
		const eventData = {
			name: item.name,
			poster: optimizedImageUrl || item.poster, // Use optimized URL in event data
			description: item.description || "",
			isLive: false, // Grid items are typically not live events
			startDate: null,
		};

		// Check if content is paid based on products existence
		// Only show lock icon for videos that actually have products (are paid)
		const isPaid =
			((item as any).products && (item as any).products.length > 0) ||
			((item as any).ItemProducts &&
				(item as any).ItemProducts.length > 0) ||
			(item as any).isPaid === true;

		return (
			<VideoCard
				id={item.itemId}
				title={item.name} // Use name instead of title
				imageUrl={optimizedImageUrl || item.poster} // Use optimized URL if available, fall back to original
				onPress={(id) => onPress(id, "video", eventData)}
				width={scale(320)}
				height={scale(180)}
				metadata={metadata}
				isPaid={isPaid}
				isAuthenticated={isAuthenticated}
			/>
		);
	};

	// Render loading indicator at the end of the list
	const renderFooter = () => {
		if (!loadingMore) {
			// Return an empty view with minimal width to maintain spacing
			return <View style={{ width: GLOBAL_STYLES.ITEM_SPACING }} />;
		}
		return (
			<View style={styles.loadingContainer}>
				<ActivityIndicator
					size="large" // Use larger size for better visibility
					color="#fff"
					style={{
						position: "absolute",
						left: scale(20),
						top: scale(80), // Position at exactly half the container height (160/2)
						transform: [{ translateY: -scale(16) }], // Adjust for half the height of the large spinner
					}}
				/>
			</View>
		);
	};

	return (
		<View style={styles.container}>
			{/* Section Header */}
			<SectionHeader title={sectionTitle} />

			{/* Videos Horizontal List */}
			<FlatList
				data={videos}
				renderItem={renderCard}
				keyExtractor={(item: VideoItem, index) => {
					// Add the section ID to make keys unique across different sections
					const sectionId =
						data._kenticoId || data._kenticoCodename || "";
					return `${sectionId}-video-${
						item.itemId || `index-${index}`
					}`;
				}}
				horizontal={true}
				numColumns={1}
				style={styles.horizontalList}
				contentContainerStyle={styles.gridContent}
				showsHorizontalScrollIndicator={false}
				// Add onEndReached to load more videos
				onEndReached={onEndReached}
				onEndReachedThreshold={0.5} // Trigger when user is halfway through the list
				// Add loading indicator at the end
				ListFooterComponent={renderFooter}
				// Performance optimizations
				removeClippedSubviews={true}
				maxToRenderPerBatch={4}
				windowSize={5}
				initialNumToRender={4}
			/>
		</View>
	);
};

export default DynamicGridWithCategorySectionRenderer;

const styles = StyleSheet.create({
	container: {
		marginBottom: GLOBAL_STYLES.SECTION_MARGIN_BOTTOM,
	},
	horizontalList: {
		width: "100%",
	},
	gridContent: {
		marginTop: scale(24), // space between section header and content #TODO make global
		gap: scale(16), // gap for spacing
	},
	loadingContainer: {
		padding: 0,
		height: scale(160), // Match the height of the video cards
		width: scale(80), // Narrower width for the spinner container
		position: "relative", // Use absolute positioning for more precise control
	},
});
