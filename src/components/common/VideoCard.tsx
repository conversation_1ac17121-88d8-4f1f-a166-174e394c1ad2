import React, { useMemo } from "react";
import {
	View,
	Text,
	Image,
	TouchableOpacity,
	StyleSheet,
} from "react-native";

// Import placeholder image from assets
const PLACEHOLDER_IMAGE = require("../../assets/images/placeholder.jpg");
import { scale } from "../../utils/helpers/dimensionScale.helper";
import { optimizeImageUrl } from "../../utils/helpers/imageOptimization.helper";
import { GLOBAL_STYLES } from "../../styles/globalStyles";
import PaidContentOverlay from "./PaidContentOverlay";

/**
 * VideoCard Component
 *
 * A reusable card component for displaying video items with:
 * - Thumbnail image
 * - Title
 * - Optional metadata (duration, date, etc.)
 * - Placeholder for missing images
 *
 * @param id - Unique identifier for the video
 * @param title - Title of the video
 * @param imageUrl - URL of the thumbnail image
 * @param onPress - Callback for when the card is pressed
 * @param width - Width of the card (default: 280)
 * @param height - Height of the thumbnail (default: 160)
 * @param metadata - Optional metadata to display below the title
 * @param isPaid - Whether this video requires payment
 * @param isAuthenticated - Whether the user is currently authenticated
 */
interface VideoCardProps {
	id: string;
	title: string;
	imageUrl?: string | null;
	onPress: (id: string, type?: string, eventData?: any) => void;
	width?: number;
	height?: number;
	metadata?: string;
	isPaid?: boolean;
	isAuthenticated?: boolean;
}

const VideoCard: React.FC<VideoCardProps> = ({
	id,
	title,
	imageUrl,
	onPress,
	width = scale(340),
	height = scale(190),
	metadata,
	isPaid = false,
	isAuthenticated = false,
}) => {
	// Optimize the image URL to prevent memory issues
	// Using the OPTIMIZED preset which has appropriate dimensions for video cards
	const optimizedImageUrl = useMemo(() => {
		return imageUrl ? optimizeImageUrl(imageUrl, "OPTIMIZED") : null;
	}, [imageUrl]);

	return (
		<TouchableOpacity
			style={[styles.container, { width, opacity: 0.7 }]}
			onPress={() =>
				onPress(id, "video", { name: title, poster: imageUrl })
			}
			activeOpacity={1}
			// Add TV specific props
			hasTVPreferredFocus={false}
			tvParallaxProperties={{ enabled: true }}
			accessible={true}
			accessibilityRole="button"
			accessibilityLabel={`Play video: ${title || "Untitled Video"}`}
		>
			<View style={[styles.thumbnailContainer, { width, height }]}>
				{optimizedImageUrl ? (
					<Image
						source={{ uri: optimizedImageUrl }}
						style={styles.thumbnail}
						resizeMode="cover"
						onError={(error) => {
							console.warn(
								`Image load error for ${title}:`,
								error.nativeEvent
							);
						}}
					/>
				) : (
					<View style={styles.placeholderContainer}>
						<Image
							source={PLACEHOLDER_IMAGE}
							style={styles.thumbnail}
							resizeMode="cover"
						/>
					</View>
				)}

				{/* Paid content overlay - shows lock icon for paid videos */}
				<PaidContentOverlay
					isPaid={isPaid}
					isAuthenticated={isAuthenticated}
				/>
			</View>

			<View style={styles.textContainer}>
				<View style={styles.titleRow}>
					<Text
						style={styles.title}
						numberOfLines={2}
						ellipsizeMode="tail"
					>
						{title || "Untitled Video"}
					</Text>

					{metadata && (
						<Text
							style={styles.metadata}
							numberOfLines={1}
						>
							{/* Extract and display just the duration part from metadata */}
							{metadata.split("•")[0].trim()}
						</Text>
					)}
				</View>
			</View>
		</TouchableOpacity>
	);
};

const styles = StyleSheet.create({
	container: {
		borderRadius: scale(10), // Slightly increased border radius
		marginRight: GLOBAL_STYLES.ITEM_SPACING, // Use global spacing for consistency
	},
	thumbnailContainer: {
		borderRadius: scale(10), // Matching border radius
		overflow: "hidden",
		backgroundColor: "#1a3c6b",
	},
	thumbnail: {
		width: "100%",
		height: "100%",
	},
	placeholderContainer: {
		width: "100%",
		height: "100%",
		overflow: "hidden",
		backgroundColor: "rgba(26, 60, 107, 0.7)",
	},
	textContainer: {
		marginTop: scale(6), // Reduced top margin to decrease spacing between image and text
	},
	titleRow: {
		flexDirection: "row",
		justifyContent: "space-between",
		alignItems: "flex-start",
		width: "100%",
	},
	title: {
		fontSize: scale(25),
		color: "#ffffff",
		fontWeight: "bold",
		flex: 1,
		marginRight: scale(12),
		lineHeight: scale(34), // Increased line height for better readability with 2 lines
		textAlign: "left", // Explicitly set left alignment
	},
	metadata: {
		fontSize: scale(23),
		color: "#ffffff",
		fontWeight: "500",
		minWidth: scale(60), // Added minimum width to ensure consistent alignment
		textAlign: "right", // Right-aligned text
	},
});

export default VideoCard;
