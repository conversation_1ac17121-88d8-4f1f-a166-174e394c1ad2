import { User<PERSON><PERSON> } from "./apis/generated/user_api";

// Create a singleton instance of UserAPI for authentication
// Using production environment to match your curl examples
export const userAPIClient = new UserAPI({
	baseURL: "https://users-service.onrewind.tv",
	headers: {
		Host: "users-service.onrewind.tv",
		"x-account-key": "ByAWCu-i5", // Production account key from your curl example
		"Accept-Charset": "UTF-8",
		"Content-Type": "application/json",
		Accept: "application/json",
		"User-Agent": "ktor-client",
		"Accept-Language": "en-GB,en;q=0.9",
	},
});

// Set authentication token for user API requests
export const setUserApiAuthToken = (token: string | null) => {
	userAPIClient.instance.defaults.headers.common["Authorization"] =
		token ? `Bearer ${token}` : "";
};
