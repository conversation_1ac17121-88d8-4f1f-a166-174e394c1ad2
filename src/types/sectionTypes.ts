import {
	AdvertisementStaticSection,
	CarouselStaticSection,
	GridStaticSection,
	GridDynamicSection,
	CarouselDynamicSection,
	LiveCarouselDynamicSection,
} from "../utils/apis/generated/kentico";

/**
 * Enum for section types from Kentico API
 */
export enum SectionType {
	STATIC_AD = "section_static_ad",
	STATIC_CAROUSEL = "section_static_carousel",
	STATIC_GRID = "section_static_grid",
	DYNAMIC_CAROUSEL = "section_dynamic_carousel",
	DYNAMIC_GRID = "section_dynamic_grid",
	DYNAMIC_LIVE = "section_dynamic_live",
	DYNAMIC_GRID_WITH_CATEGORY = "section_dynamic_grid_with_category",
}

/**
 * Base section props that all section renderers will receive
 */
export interface BaseSectionRendererProps<T> {
	data: T;
	onPress?: (id: string, type: string, eventData?: any) => void;
	accessibilityLabel?: string;
}

/**
 * Props for the Ad Section Renderer
 */
export interface AdSectionRendererProps
	extends BaseSectionRendererProps<AdvertisementStaticSection> {}

/**
 * Props for the Carousel Section Renderer
 */
export interface CarouselSectionRendererProps
	extends BaseSectionRendererProps<
		| CarouselStaticSection
		| CarouselDynamicSection
		| LiveCarouselDynamicSection
		| GridStaticSection
		| GridDynamicSection
	> {
	isAuthenticated?: boolean; // Authentication state for paid content overlays
}

/**
 * Props for section renderer components
 */
export interface SectionRendererProps {
	section: any; // Using any as the section can be of various types
	onItemPress: (id: string, type: string, eventData?: any) => void;
	upcomingEvents?: any[]; // Optional upcoming events to be passed to LiveSectionRenderer
	isAuthenticated?: boolean; // Authentication state for paid content overlays
}

/**
 * Type that ensures all section data has an items or Videos property
 */
export type SectionWithItems = {
	_kenticoCodename: string;
	items?: any[];
	Videos?: any[];
	title?: string;
	name?: string;
};
