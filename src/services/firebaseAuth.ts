/**
 * Firebase Authentication Service
 * Handles real Firebase authentication and provides the Firebase JWT token
 * that gets exchanged for app access tokens via the OnRewind API
 */

export interface FirebaseUser {
	uid: string;
	email: string;
	displayName?: string;
	emailVerified: boolean;
}

export interface FirebaseAuthResult {
	user: FirebaseUser;
	token: string; // Real Firebase JWT token for OnRewind API
}

export interface FirebaseAuthError {
	code: string;
	message: string;
}

// Alternative approach: Use pre-generated Firebase token
// This bypasses Firebase authentication entirely and uses the OnRewind API directly

/**
 * Firebase Authentication Service
 * Performs real Firebase authentication and returns a Firebase JWT token
 * that will be exchanged for app tokens via OnRewind API
 */
export class FirebaseAuth {
	/**
	 * Alternative approach: Bypass Firebase authentication
	 * Use pre-generated Firebase token to get OnRewind access tokens directly
	 * This matches your curl example exactly
	 */
	static async signInWithEmailAndPassword(
		email: string,
		password: string
	): Promise<FirebaseAuthResult> {
		try {
			console.log(
				"Firebase: Using alternative approach for email:",
				email
			);

			// Basic validation
			if (!email || !password) {
				throw {
					code: "invalid-credentials",
					message: "Email and password are required",
				};
			}

			// Basic email validation
			const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
			if (!emailRegex.test(email)) {
				throw {
					code: "invalid-email",
					message: "Please enter a valid email address",
				};
			}

			// Basic password validation
			if (password.length < 6) {
				throw {
					code: "weak-password",
					message: "Password must be at least 6 characters long",
				};
			}

			// Use the pre-generated Firebase token from your curl example
			// This bypasses Firebase authentication entirely
			const freshFirebaseToken =
				"eyJhbGciOiJSUzI1NiIsImtpZCI6Ijg3NzQ4NTAwMmYwNWJlMDI2N2VmNDU5ZjViNTEzNTMzYjVjNThjMTIiLCJ0eXAiOiJKV1QifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.nla1Njapv76N6vit8szJNsVzq4cKK22kqJxov6J5tnj_DYs18T50ByHD5LlI5EW4g8jOnzDTbGeN2-iPtpwPh0dsbpdv-us8bkK_mR6x-WwAnWeULFL6GuG-tt5wd9_J1gA0rhK_G7IOSeAoGiu4gNPHhXCQTBjkBb9Lp6fdh0arasLiosVZYtJ33o_7hLGi6iL-KXfjDGcuIvOuefIjawjL-Gpf8y-ifgs6x3FoQ0k8dZbZ6ol892ci5yJjqAa5XtU7EC3BIO_JvaslKpSeswGXkVlAZFtHqmiO_WlctA6ki5s2Wau7zSJtR4-__Erkp80XxENL8RS3h9StCpI_VQ";
			console.log(
				"Firebase: Using fresh token from web (bypassing Firebase auth)"
			);
			console.log(
				"Firebase: Fresh token (first 50 chars):",
				freshFirebaseToken.substring(0, 50) + "..."
			);

			// Create user object based on the token content
			const user: FirebaseUser = {
				uid: "pc0hNnkgl8QgfMg4CcMjCHM3SVi1", // From the token payload
				email: email,
				displayName: "Olya Lelya Test Hello this is test", // From the token payload
				emailVerified: true,
			};

			return {
				user,
				token: freshFirebaseToken,
			};
		} catch (error: any) {
			console.error("Firebase authentication error:", error);

			// If error already has our format, throw it
			if (error.code && error.message) {
				throw error as FirebaseAuthError;
			}

			// Convert other errors to our format
			const firebaseError: FirebaseAuthError = {
				code: error.code || "unknown",
				message: error.message || "Authentication failed",
			};

			throw firebaseError;
		}
	}

	/**
	 * Convert Firebase error codes to user-friendly messages
	 */
	private static getReadableErrorMessage(errorCode: string): string {
		switch (errorCode) {
			case "EMAIL_NOT_FOUND":
				return "No account found with this email address.";
			case "INVALID_PASSWORD":
				return "Incorrect password. Please try again.";
			case "USER_DISABLED":
				return "This account has been disabled.";
			case "TOO_MANY_ATTEMPTS_TRY_LATER":
				return "Too many failed attempts. Please try again later.";
			case "INVALID_EMAIL":
				return "Please enter a valid email address.";
			case "WEAK_PASSWORD":
				return "Password must be at least 6 characters long.";
			case "EMAIL_EXISTS":
				return "An account with this email already exists.";
			default:
				return "Authentication failed. Please try again.";
		}
	}
}
