/**
 * HomeGridPage Component
 *
 * This is the main landing page of the application that displays:
 * - Video grid layout with different sections
 * - Live and upcoming events
 * - Categories and playlists
 * - Advertisement banners
 * - Language selector
 * - Authentication status
 *
 * The component handles:
 * - Data fetching from multiple endpoints
 * - Responsive grid layouts
 * - Platform-specific optimizations (TV vs Mobile)
 * - Authentication state
 * - Language switching
 * - Navigation to video player
 */

// React and core functionality
import React, {
	useEffect,
	useState,
	useCallback,
	useMemo,
} from "react";
// React Native UI components
import {
	StyleSheet,
	Text,
	View,
	FlatList,
	TouchableOpacity,
	Image,
	Platform,
	StyleProp,
	ImageStyle,
} from "react-native";
// Type definitions from Kentico API
import {
	CategoryCard,
	KenticoAPI,
	PlaylistCard,
	VideoCard,
} from "../utils/apis/generated/kentico";
// API clients and authentication
import {
	kenticoAPIClient,
	setAuthToken,
} from "../utils/kenticoInstance";
import { setMainApiAuthToken } from "../utils/mainApiInstance";
// Additional type definitions
import { Image as KenticoImage } from "../utils/apis/generated/kentico";
import { KenticoImageType } from "../utils/apis/generated/kentico";
import { Video as KenticoVideo } from "../utils/apis/generated/kentico";
import { SectionContent } from "../utils/apis/generated/kentico";
import { CarouselStaticSection } from "../utils/apis/generated/kentico";
// Helper functions
import { fetchCarouselSections } from "../utils/kenticoHelpers";
// Navigation utilities
import {
	useNavigation,
	useTheme,
	useFocusEffect,
} from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { RootStackParamList } from "../app/index";
// Data types
import {
	PageLayout,
	OriginsEventArray,
} from "../utils/apis/generated/kentico";
// API helper functions
import {
	fetchHomePageData,
	fetchLiveEvents,
	fetchFutureEvents,
} from "../utils/kenticoHelpers";
// Components
import LanguageSelector from "../components/homePageComponents/LanguageSelector";
import { AUTH_TOKEN } from "../config/auth";
import AdBanner from "../components/homePageComponents/AdBanner";
import { AdSection } from "../types/kentico";
import SplashScreen from "../components/homePageComponents/SplashScreen";
// Image optimization utilities
import {
	optimizeImageUrl,
	PRESETS,
} from "../utils/helpers/imageOptimization.helper";
import { scale } from "../utils/helpers/dimensionScale.helper";
import { formatDuration } from "../utils/helpers/formatDuration.helper";
import { GLOBAL_STYLES } from "../styles/globalStyles";

/**
 * Constants
 */
// Duration to show splash screen in milliseconds
const SPLASH_SCREEN_DURATION = 3000;

/**
 * Performance Configuration
 * These settings are crucial for optimal performance on TV platforms
 * Carefully tuned values to prevent frame drops and maintain smooth scrolling
 */
const LIST_CONFIG = {
	BATCH_SIZE: 5, // Number of items to render in each batch
	WINDOW_SIZE: 5, // Number of screens worth of content to keep rendered
	INITIAL_RENDER: 5, // Initial number of items to render on mount
};

/**
 * VideoItem Props Interface
 * Defines the required and optional props for video items in the grid
 */
interface VideoItemProps {
	item: {
		_kenticoId?: string; // Unique identifier from Kentico
		_kenticoItemType?: string; // Type of content (video, event, etc.)
		itemType?: "video" | "event"; // Specific type of media
		itemId?: string; // Unique identifier for the item
		name?: string; // Display name
		title?: string; // Alternative title
		description?: string; // Content description
		products?: any[]; // Associated products (for premium content)
		image?: {
			// Image assets
			image?: { url?: string };
			mobileImage?: { url?: string };
		};
		thumbnail?: string; // Thumbnail URL
		poster?: string; // Poster image URL
		heroLandscape?: string; // Landscape hero image URL
		heroPortrait?: string; // Portrait hero image URL
		event?: {
			// Event specific data
			itemId: string;
			name: string;
			description?: string;
			startDate?: string;
		};
		isInTheNews?: boolean; // Featured content flag
		isCategories?: boolean; // Category content flag
		// Add CategoryCard properties
		id?: string;
		duration?: string | number | null; // Duration of the video
	};
	index: number; // Position in the list
	isAuthenticated: boolean; // User authentication status
	navigation: NativeStackNavigationProp<RootStackParamList>; // Navigation prop
	currentLanguage: string; // Current UI language
	inTheNewsCat: boolean; // Featured section flag
	renderImage: (
		imageUrl: string | null | undefined,
		style: StyleProp<ImageStyle> | undefined
	) => React.ReactNode;
	renderLockOverlay: (isAuth: boolean) => React.ReactNode; // Premium content overlay
}

// Define placeholder image at module level
const PLACEHOLDER_IMAGE = require("../assets/images/placeholder.jpg");

// Define constants for item dimensions
const VIDEO_ITEM_WIDTH = scale(360);
const COMING_LIVE_ITEM_WIDTH = scale(432);
const IN_THE_NEWS_ITEM_WIDTH = scale(600);

/**
 * VideoItem Component
 * Renders individual video items in the grid
 * Uses React.memo for performance optimization
 */
const VideoItem = React.memo(
	({
		item,
		index,
		isAuthenticated,
		navigation,
		currentLanguage,
		inTheNewsCat,
		renderImage,
		renderLockOverlay,
	}: VideoItemProps) => {
		// Check if content is paid based on having products
		const isPaid = item.products && item.products.length > 0;

		/**
		 * Get the most appropriate image URL based on available assets
		 * Follows a priority order:
		 * 1. Kentico image
		 * 2. Thumbnail
		 * 3. Poster
		 * 4. Hero images
		 */
		const getImageUrl = () => {
			if (item._kenticoItemType === "image") {
				const url =
					item.image?.image?.url || item.image?.mobileImage?.url;
				return url;
			}
			if (item.thumbnail) {
				return item.thumbnail;
			}
			if (item.poster) {
				return item.poster;
			}
			if (item.heroLandscape || item.heroPortrait) {
				const url = item.heroLandscape || item.heroPortrait;
				return url;
			}
			return null;
		};

		const imageUrl = getImageUrl();

		/**
		 * Handle item press/selection
		 * Navigates to video player if item is playable
		 * Navigates to Categories page if item is a category
		 * Navigates to Competition page if item is a competition
		 */
		const handlePress = useCallback(() => {
			// Handle competition items
			if ((item as any).isCompetition && (item as any).pageCodename) {
				console.log(
					`Navigating to competition page: ${
						(item as any).pageCodename
					}`
				);
				navigation.navigate("CompetitionsPage", {
					pageCodename: (item as any).pageCodename,
				});
				return;
			}

			// Handle category items
			if (item.isCategories) {
				// Navigate to Categories page with category data
				navigation.navigate("Categories", {
					categoryId: item.id || item._kenticoId,
					categoryName: item.name,
					categoryImage:
						item.thumbnail ||
						item.image?.image?.url ||
						item.heroLandscape ||
						"",
				});
				return;
			}

			// Handle video items
			if (item._kenticoItemType !== "image") {
				const isLive = item.itemType === "event";
				const videoId =
					isLive && item.event ? item.event.itemId : item.itemId;

				if (!videoId) return;

				// Navigate to video player with required data
				navigation.navigate("VideoDetailsPage", {
					video: {
						videoId,
						title: item.name || item.event?.name || "",
						thumbnail: imageUrl || "",
						description:
							item.description || item.event?.description || "",
						isLive,
						startTime: isLive ? item.event?.startDate : undefined,
					},
				});
			}
		}, [item, imageUrl, navigation]);

		/**
		 * Style Computations
		 * Combines base styles with conditional styles based on content type
		 */
		const containerStyle = [
			styles.videoItem,
			inTheNewsCat && styles.inTheNewsVideoItem,
		];

		const thumbnailContainerStyle = [
			styles.thumbnailContainer,
			inTheNewsCat && styles.inTheNewsThumbnailContainer,
		];

		const thumbnailStyle = [
			styles.thumbnail,
			inTheNewsCat && styles.inTheNewsThumbnail,
		];

		return (
			<TouchableOpacity
				style={[
					styles.videoItem,
					inTheNewsCat && styles.inTheNewsVideoItem,
				]}
				onPress={handlePress}
				activeOpacity={1}
			>
				<View
					style={[
						styles.thumbnailContainer,
						inTheNewsCat && styles.inTheNewsThumbnailContainer,
					]}
				>
					{renderImage(imageUrl, [
						styles.thumbnail,
						inTheNewsCat && styles.inTheNewsThumbnail,
					])}
					{isPaid && renderLockOverlay(isAuthenticated)}
					{item.duration && !item.isCategories && (
						<View style={styles.durationContainer}>
							<Text style={styles.durationText}>
								{formatDuration(item.duration)}
							</Text>
						</View>
					)}
				</View>
				<Text
					style={[
						styles.videoTitle,
						inTheNewsCat && styles.inTheNewsVideoTitle,
					]}
					numberOfLines={2}
				>
					{item.name || item.title || "Untitled"}
				</Text>
			</TouchableOpacity>
		);
	},
	// Custom comparison function to prevent unnecessary re-renders
	(prevProps, nextProps) => {
		return (
			prevProps.item._kenticoId === nextProps.item._kenticoId &&
			prevProps.isAuthenticated === nextProps.isAuthenticated &&
			prevProps.currentLanguage === nextProps.currentLanguage &&
			prevProps.inTheNewsCat === nextProps.inTheNewsCat
		);
	}
);

/**
 * ComingLiveItem Props Interface
 * Defines the required and optional props for upcoming live event items
 */
interface ComingLiveItemProps {
	item: {
		event: any; // Event data
		isLive: boolean; // Current live status
	};
	index: number; // Position in the list
	isAuthenticated: boolean; // User authentication status
	navigation: NativeStackNavigationProp<RootStackParamList>; // Navigation prop
	currentLanguage: string; // Current UI language
	renderImage: (
		imageUrl: string | null | undefined,
		style: StyleProp<ImageStyle> | undefined
	) => React.ReactNode;
	renderLockOverlay: (isAuth: boolean) => React.ReactNode; // Premium content overlay
}

/**
 * ComingLiveItem Component
 * Renders individual live or upcoming event items
 * Uses React.memo for performance optimization
 */
const ComingLiveItem = React.memo(
	({
		item,
		index,
		isAuthenticated,
		navigation,
		currentLanguage,
		renderImage,
		renderLockOverlay,
	}: ComingLiveItemProps) => {
		/**
		 * Handle item press/selection
		 * Navigates to video player for both live and upcoming events
		 * Live events are immediately playable, upcoming events show details
		 */
		const handlePress = useCallback(() => {
			// Always navigate for live events
			if (item.event.itemId) {
				navigation.navigate("VideoDetailsPage", {
					video: {
						videoId: item.event.itemId,
						title: item.event.name,
						thumbnail: item.event.poster || "",
						description: item.event.description || "",
						isLive: item.isLive,
						startTime: item.event.startDate,
					},
				});
			}
		}, [item, navigation]);

		/**
		 * Format event date and time
		 * Converts event date to user-friendly format based on current language
		 * @param dateString - ISO date string
		 * @returns {object} - Formatted date text and time
		 */
		const formatEventDateTime = (
			dateString: string | null | undefined
		) => {
			if (!dateString) return { text: "Tomorrow", time: "15:55" };
			const date = new Date(dateString);
			const now = new Date();
			const tomorrow = new Date(now);
			tomorrow.setDate(tomorrow.getDate() + 1);

			let text = "Tomorrow";
			if (date.toDateString() === now.toDateString()) {
				text = "Today";
			} else if (date.getDate() !== tomorrow.getDate()) {
				text = date.toLocaleDateString(currentLanguage, {
					month: "long",
					day: "numeric",
				});
			}

			const time = date.toLocaleTimeString(currentLanguage, {
				hour: "2-digit",
				minute: "2-digit",
				hour12: false,
			});

			return { text, time };
		};

		const { text, time } = formatEventDateTime(item.event.startDate);

		return (
			<TouchableOpacity
				style={[
					styles.comingLiveVideoItem,
					item.isLive && styles.liveItem,
				]}
				onPress={handlePress}
				activeOpacity={1}
			>
				<View style={styles.comingLiveThumbnailContainer}>
					{renderImage(item.event.poster, styles.comingLiveThumbnail)}
					{!isAuthenticated && renderLockOverlay(isAuthenticated)}
					<View style={styles.eventInfoOverlay}>
						<View style={styles.eventTimeRow}>
							<Text style={styles.eventTimeText}>{text}</Text>
							<View style={styles.timeContainer}>
								<Text style={styles.clockIcon}>⏰</Text>
								<Text style={styles.eventTimeText}>{time}</Text>
							</View>
						</View>
						<Text
							style={
								item.isLive
									? styles.liveIndicator
									: styles.upcomingIndicator
							}
						>
							{item.isLive ? "LIVE" : "UPCOMING"}
						</Text>
					</View>
				</View>
				<View style={styles.belowImageContent}>
					<Text style={styles.videoTitle}>{item.event.name}</Text>
					<Text style={styles.eventDescription}>
						{item.event.description || ""}
					</Text>
				</View>
			</TouchableOpacity>
		);
	},
	// Custom comparison function to prevent unnecessary re-renders
	(prevProps, nextProps) => {
		return (
			prevProps.item.event.itemId === nextProps.item.event.itemId &&
			prevProps.isAuthenticated === nextProps.isAuthenticated &&
			prevProps.currentLanguage === nextProps.currentLanguage
		);
	}
);

/**
 * Event Interface
 * Defines the structure of event data used in the application
 */
interface Event {
	event: {
		itemId: string; // Unique identifier
		name: string; // Event name
		description?: string | null; // Event description
		startDate?: string | null; // Event start time
		poster?: string | null; // Event poster image
		onrewindState?: string | null; // Playback state
		shareUrl?: string | null; // Social sharing URL
		urlSlug?: string | null; // SEO-friendly URL
	};
	isLive: boolean; // Current live status
	_kenticoCodename?: string; // Kentico identifier
	_kenticoId?: string; // Kentico system ID
	_kenticoItemType?: string; // Content type in Kentico
	_kenticoLanguage?: string; // Content language
	itemType?: "event"; // Type identifier
}

/**
 * FetchHomePage Component
 * Main component for the home page that handles:
 * - Data fetching from multiple endpoints
 * - State management for videos, events, and UI
 * - Authentication state
 * - Language selection
 * - Navigation
 */
const FetchHomePage = () => {
	// Navigation hook for screen transitions
	const navigation =
		useNavigation<NativeStackNavigationProp<RootStackParamList>>();

	/**
	 * State Management
	 * Multiple states to handle different aspects of the home page
	 */
	// Content states
	const [homePageData, setHomePageData] = useState<
		CarouselStaticSection[] | null
	>(null);
	const [liveEvents, setLiveEvents] =
		useState<OriginsEventArray | null>(null);
	const [futureEvents, setFutureEvents] =
		useState<OriginsEventArray | null>(null);
	const [adSection, setAdSection] = useState<AdSection | null>(null);
	const [response, setResponse] = useState<any>(null);

	// UI states
	const [loading, setLoading] = useState<boolean>(true);
	const [error, setError] = useState<string | null>(null);
	const [currentLanguage, setCurrentLanguage] = useState("en");
	const [isAuthenticated, setIsAuthenticated] = useState(true);
	const [showSplash, setShowSplash] = useState(true);
	const [isAuthButtonFocused, setIsAuthButtonFocused] =
		useState(false);

	/**
	 * Fetch Home Page Data
	 * Retrieves carousel components and ad sections from the Kentico API
	 * Updates state with fetched data
	 */
	const fetchHomePageData = useCallback(async () => {
		const startTime = Date.now(); // Start timing
		try {
			// Fetch page data with current language
			const response = await kenticoAPIClient.ott.getPage("home", {
				language: currentLanguage,
				previewFlag: false,
			});

			// Log the entire response structure to find grid_home_page___competitions
			// console.log(
			// 	"HOME PAGE RESPONSE STRUCTURE:",
			// 	JSON.stringify(response.data, null, 2)
			// );
			// console.log(
			// 	"Looking for grid_home_page___competitions in the response..."
			// );

			// Log all section types from HomePage
			console.log("HOME PAGE SECTIONS:");
			response.data.components.forEach((section: any) => {
				console.log(
					`Section Type: ${section._kenticoItemType}, Codename: ${section._kenticoCodename}`
				);
			});

			// Filter carousel components from response
			const carouselComponents = response.data.components.filter(
				(component: any) =>
					[
						"section_static_carousel",
						"section_dynamic_carousel",
					].includes(component._kenticoItemType)
			) as CarouselStaticSection[];
			setHomePageData(carouselComponents);

			// Extract ad component if present
			const adComponent = response.data.components.find(
				(component: any) =>
					component._kenticoItemType === "section_static_ad"
			);
			if (adComponent) {
				setAdSection(adComponent as unknown as AdSection);
			}

			// Look for the grid_home_page___competitions section in the response
			const competitionsSection = response.data.components.find(
				(component: any) =>
					component._kenticoCodename ===
					"grid_home_page___competitions"
			);

			if (competitionsSection) {
				console.log(
					"Found competitions section in home page response:",
					competitionsSection._kenticoCodename
				);

				// Log competition items count
				console.log(
					"Competitions items count:",
					(competitionsSection as any).items?.length || 0
				);

				// Mark this section as a competitions section for special rendering
				(competitionsSection as any).isCompetitionsSection = true;
			} else {
				console.warn(
					"Competitions section 'grid_home_page___competitions' not found in home page response"
				);
			}

			setResponse(response);
		} catch (err) {
			console.error("Error fetching home page data:", err);
			setError("Failed to fetch HOME page data");
		} finally {
			const endTime = Date.now(); // End timing
			console.log(`fetchHomePageData took ${endTime - startTime} ms`); // Log the time taken
		}
	}, [currentLanguage]);

	/**
	 * Fetch Live Events
	 * Retrieves currently live events from the Kentico API
	 * Updates state with live events data
	 */
	const fetchLiveEventsData = useCallback(async () => {
		try {
			const response = await kenticoAPIClient.ott.getEventsV2({
				status: "live",
				date: new Date().toISOString().split("T")[0],
			});
			setLiveEvents(response.data);
		} catch (err) {
			console.error("Error fetching live events:", err);
			setError("Failed to fetch LIVE events");
		}
	}, []);

	/**
	 * Fetch Future Events
	 * Retrieves upcoming events from the Kentico API
	 * Updates state with future events data
	 */
	const fetchFutureEventsData = useCallback(async () => {
		try {
			// Set time to start of day for consistent comparison
			const today = new Date();
			today.setHours(0, 0, 0, 0);

			const response = await kenticoAPIClient.ott.getEventsV2({
				status: "future",
				date: today.toISOString(),
			});
			setFutureEvents(response.data);
		} catch (err) {
			console.error("Error fetching future events:", err);
			setError("Failed to fetch FUTURE events");
		}
	}, []);

	/**
	 * Language Change Handler
	 * Updates current language and triggers data refresh
	 */
	const handleLanguageChange = (language: string) => {
		setCurrentLanguage(language);
	};

	/**
	 * Authentication Toggle Handler
	 * Toggles authentication state and refreshes data
	 * Updates auth tokens for both API clients
	 */
	const toggleAuth = useCallback(() => {
		setIsAuthenticated((prev) => {
			if (prev) {
				setAuthToken(null);
				setMainApiAuthToken(null);
			} else {
				setAuthToken(AUTH_TOKEN);
				setMainApiAuthToken(AUTH_TOKEN);
			}
			return !prev; // Toggle the authentication state
		});
		// Refresh all data with new auth state
		fetchHomePageData();
		fetchLiveEventsData();
		fetchFutureEventsData();
	}, [fetchHomePageData, fetchLiveEventsData, fetchFutureEventsData]);

	/**
	 * Data Refresh Functions
	 */

	/**
	 * Initial Data Load Effect
	 * Handles initial data loading when component mounts
	 * Includes splash screen timing and authentication setup
	 */
	useEffect(() => {
		// Set up authentication tokens
		setAuthToken(AUTH_TOKEN);
		setMainApiAuthToken(AUTH_TOKEN);

		const fetchData = async () => {
			setLoading(true);
			setError(null);
			try {
				// Load live events first for immediate display
				await fetchLiveEventsData();

				// Ensure minimum splash screen duration
				const minSplashPromise = new Promise((resolve) =>
					setTimeout(resolve, SPLASH_SCREEN_DURATION)
				);

				// Load remaining data in parallel
				const dataPromise = Promise.all([
					fetchHomePageData(),
					fetchFutureEventsData(),
				]);

				// Wait for both splash screen and data loading
				await Promise.all([minSplashPromise, dataPromise]);
				setShowSplash(false);

				// If needed, live events will be refreshed when the screen comes into focus via useFocusEffect
			} catch (error) {
				console.error("Error loading initial data:", error);
				setError("Failed to load initial data");
			} finally {
				setLoading(false);
			}
		};

		fetchData();
	}, [currentLanguage]);

	/**
	 * Focus Effect
	 * Refreshes live events when screen comes into focus
	 * We only refresh live events, not the entire home page data
	 * to avoid unnecessary re-renders and duplicate logs
	 */
	useFocusEffect(
		useCallback(() => {
			if (!loading) {
				// Only refresh live events, not the entire home page data
				// This prevents unnecessary re-renders and duplicate logs
				fetchLiveEventsData();
			}
		}, [loading, fetchLiveEventsData])
	);

	/**
	 * Image Rendering Functions
	 * Handle image display with optimization and fallbacks
	 */
	const renderImage = useCallback(
		(
			imageUrl: string | null | undefined,
			style: StyleProp<ImageStyle> | undefined
		) => {
			const safeStyle = style || {};
			// console.log("[renderImage] Original URL:", imageUrl);

			if (!imageUrl) {
				// console.log(
				// 	"[renderImage] No URL provided, using placeholder"
				// );
				return (
					<Image
						source={PLACEHOLDER_IMAGE}
						style={safeStyle}
					/>
				);
			}

			try {
				// Skip optimization for local file URLs
				const shouldOptimize = imageUrl.startsWith("http");
				const finalUrl = shouldOptimize
					? optimizeImageUrl(imageUrl, PRESETS.OPTIMIZED)
					: imageUrl;
				// console.log("[renderImage] Final URL:", finalUrl);
				return (
					<Image
						source={finalUrl ? { uri: finalUrl } : PLACEHOLDER_IMAGE}
						style={safeStyle}
						onError={(error) => {
							console.error("[renderImage] Image load error:", error);
							// Fallback to placeholder on error
							return (
								<Image
									source={PLACEHOLDER_IMAGE}
									style={safeStyle}
								/>
							);
						}}
					/>
				);
			} catch (error) {
				console.error("[renderImage] Error rendering image:", error);
				return (
					<Image
						source={PLACEHOLDER_IMAGE}
						style={safeStyle}
					/>
				);
			}
		},
		[]
	);

	/**
	 * Premium Content Overlay
	 * Renders a lock icon overlay for premium content
	 * For non-authenticated users: full red overlay with centered lock
	 * For authenticated users: small lock icon in top-right corner
	 */
	const renderLockOverlay = useCallback(
		(isAuth: boolean) => {
			if (!isAuth) {
				// Non-authenticated users see red overlay with centered lock
				return (
					<View style={styles.lockOverlay}>
						<View style={styles.lockIconContainerLarge}>
							<Text style={styles.lockIconLarge}>🔒</Text>
						</View>
					</View>
				);
			} else {
				// Authenticated users see small lock icon in top-right
				return (
					<View style={styles.lockIconWrapper}>
						<View style={styles.lockIconContainer}>
							<Text style={styles.lockIcon}>🔒</Text>
						</View>
					</View>
				);
			}
		},
		[] // No dependencies needed
	);

	/**
	 * List Optimization Functions
	 * Memoized functions for FlatList performance
	 */
	// Generic getItemLayout function for video items
	const getVideoItemLayout = useCallback(
		(_data: any, index: number) => ({
			length: VIDEO_ITEM_WIDTH,
			offset: VIDEO_ITEM_WIDTH * index,
			index,
		}),
		[]
	);

	// Generic getItemLayout function for live event items
	const getLiveItemLayout = useCallback(
		(_data: any, index: number) => ({
			length: COMING_LIVE_ITEM_WIDTH,
			offset: COMING_LIVE_ITEM_WIDTH * index,
			index,
		}),
		[]
	);

	/**
	 * Coming Lives Data Processing
	 * Combines and sorts live and future events
	 * Memoized to prevent unnecessary recalculation
	 */
	const getComingLives = useMemo((): Event[] => {
		const now = new Date();
		const events = [
			// Map live events with isLive flag
			...(liveEvents || []).map((event) => ({
				...event,
				isLive: true,
			})),
			// Filter and map future events
			...(futureEvents || [])
				.filter((event) => {
					const startDate = event.event.startDate
						? new Date(event.event.startDate)
						: null;
					if (!startDate) return false;
					return startDate >= now;
				})
				.map((event) => ({ ...event, isLive: false })),
		].sort((a, b) => {
			// Sort by start date
			const dateA = a.event.startDate
				? new Date(a.event.startDate)
				: new Date(0);
			const dateB = b.event.startDate
				? new Date(b.event.startDate)
				: new Date(0);
			return dateA.getTime() - dateB.getTime();
		});
		return events;
	}, [liveEvents, futureEvents]);

	/**
	 * Memoized Render Functions
	 * Optimized renderers for list items
	 */
	// Video item renderer
	const renderVideoItem = useCallback(
		({ item, index }) => (
			<VideoItem
				item={item}
				index={index}
				isAuthenticated={isAuthenticated}
				navigation={navigation}
				currentLanguage={currentLanguage}
				inTheNewsCat={item.isInTheNews}
				renderImage={renderImage}
				renderLockOverlay={renderLockOverlay}
			/>
		),
		[
			isAuthenticated,
			navigation,
			currentLanguage,
			renderImage,
			renderLockOverlay,
		]
	);

	/**
	 * Video Section Renderer
	 * Renders a complete section of videos with title and horizontal list
	 */
	const renderVideoSection = useCallback(
		({ item }: { item: CarouselStaticSection }) => {
			// Check if this is a competitions section
			if ((item as any).isCompetitionsSection) {
				// Render competitions directly in the HomePage
				return (
					<View style={[styles.section, styles.competitionSection]}>
						{/* Add section title if available */}
						{item.title && (
							<View style={styles.sectionHeader}>
								<Text style={styles.sectionTitle}>{item.title}</Text>
							</View>
						)}
						<FlatList
							data={(item as any).items || []}
							horizontal
							showsHorizontalScrollIndicator={false}
							keyExtractor={(item: any) => {
								// Use Kentico ID as the primary key for consistent identification
								return (
									item._kenticoId ||
									item._kenticoCodename ||
									item.itemId ||
									item.id
								);
							}}
							contentContainerStyle={{
								paddingRight: scale(24),
							}}
							ItemSeparatorComponent={() => (
								<View style={{ width: scale(18) }} />
							)}
							renderItem={(props) => (
								<CompetitionItem
									item={props.item}
									navigation={navigation}
								/>
							)}
							removeClippedSubviews={true}
							maxToRenderPerBatch={LIST_CONFIG.BATCH_SIZE}
							windowSize={LIST_CONFIG.WINDOW_SIZE}
							initialNumToRender={LIST_CONFIG.INITIAL_RENDER}
						/>
					</View>
				);
			}

			// Check if this is a news or categories section
			const isInTheNews =
				item.title?.toLowerCase().includes("à la une") ||
				item.title?.toLowerCase().includes("in the news");

			// Check for both spellings of "categories" with and without accent
			const isCategories =
				item.title?.toLowerCase().includes("catégories") ||
				item.title?.toLowerCase().includes("categories");

			return (
				<View style={styles.section}>
					<View style={styles.sectionHeader}>
						<Text style={styles.sectionTitle}>{item.title}</Text>
					</View>
					<FlatList
						data={item.items as VideoCard[]}
						renderItem={({ item: videoItem, index }) =>
							renderVideoItem({
								item: {
									...videoItem,
									isInTheNews,
									isCategories,
									// Handle competition items
									isCompetition: (videoItem as any).isCompetition,
								},
								index,
							})
						}
						keyExtractor={(video) => {
							// Use Kentico ID as the primary key for consistent identification
							return (
								video._kenticoId ||
								video.itemId ||
								String(video._kenticoCodename || "")
							);
						}}
						horizontal
						showsHorizontalScrollIndicator={false}
						removeClippedSubviews={true}
						maxToRenderPerBatch={LIST_CONFIG.BATCH_SIZE}
						windowSize={LIST_CONFIG.WINDOW_SIZE}
						initialNumToRender={LIST_CONFIG.INITIAL_RENDER}
						getItemLayout={getVideoItemLayout}
					/>
				</View>
			);
		},
		[navigation, renderVideoItem, getVideoItemLayout]
	);

	/**
	 * Advertisement Banner Renderer
	 * Renders ad banner if available
	 */
	const renderAdBanner = useCallback((adSection: AdSection) => {
		if (!adSection?.image?.[0]) return null;

		const adData = adSection.image[0];
		return (
			<AdBanner
				imageUrl={adData.image.url}
				mobileImageUrl={adData.mobileImage?.url || adData.image.url}
				redirectionTarget={
					adData.buttonRedirectionUrl || adSection.redirectionTarget
				}
			/>
		);
	}, []);

	/**
	 * Competition Item Component
	 */
	const CompetitionItem = React.memo(
		({ item, navigation }: { item: any; navigation: any }) => {
			// Handle navigation to competition page
			const handlePress = useCallback(() => {
				// Extract the page codename from the mobileRedirectionTarget property
				// The format is typically "hbtv://page_competition_name"
				let pageCodename = null;

				// Check if we have mobileRedirectionTarget in the image property
				if (item.image?.mobileRedirectionTarget) {
					// Extract the page codename from the URL format "hbtv://page_name"
					const match =
						item.image.mobileRedirectionTarget.match(/hbtv:\/\/(.*)/);
					if (match && match[1]) {
						pageCodename = match[1];
					}
				}

				// Fallback to other possible sources if mobileRedirectionTarget is not available
				if (!pageCodename) {
					pageCodename =
						item.redirectionTarget?.web_menu_item
							?.redirectionTarget || item.pageCodename;
				}

				if (pageCodename) {
					console.log(
						`Navigating to competition page: ${pageCodename}`
					);
					navigation.navigate("CompetitionsPage", {
						pageCodename: pageCodename,
					});
				} else {
					console.warn(
						"No redirection target found for competition item",
						item
					);
				}
			}, [item, navigation]);

			// Get the competition name from the appropriate property
			const competitionName =
				item.name || item.title || "Unknown Competition";

			// Get the thumbnail from the appropriate property
			const thumbnail =
				item.thumbnail ||
				item.image?.url ||
				item.image?.image?.url ||
				item.logo?.url;

			// Optimize the thumbnail URL using imgix parameters
			const optimizedThumbnail = thumbnail
				? optimizeImageUrl(thumbnail, {
						...PRESETS.OPTIMIZED,
						quality: 85, // Higher quality for competition thumbnails
						width: 280, // Match the width of competition cards
						height: 160, // Appropriate height for the aspect ratio
				  })
				: null;

			return (
				<TouchableOpacity
					style={styles.competitionItem}
					onPress={handlePress}
					activeOpacity={1}
					accessible={true}
					accessibilityRole="button"
					accessibilityLabel={`Competition: ${competitionName}`}
				>
					<View style={styles.competitionImageContainer}>
						{optimizedThumbnail ? (
							<Image
								source={{ uri: optimizedThumbnail }}
								style={styles.competitionImage}
								resizeMode="cover"
							/>
						) : (
							<Image
								source={PLACEHOLDER_IMAGE}
								style={styles.competitionImage}
								resizeMode="cover"
							/>
						)}
					</View>
					<Text
						style={styles.competitionTitle}
						numberOfLines={2}
					>
						{competitionName}
					</Text>
				</TouchableOpacity>
			);
		}
	);

	/**
	 * Coming Live Item Renderer
	 */
	const renderComingLiveItem = useCallback(
		({ item, index }) => (
			<ComingLiveItem
				item={item}
				index={index}
				isAuthenticated={isAuthenticated}
				navigation={navigation}
				currentLanguage={currentLanguage}
				renderImage={renderImage}
				renderLockOverlay={renderLockOverlay}
			/>
		),
		[
			isAuthenticated,
			navigation,
			currentLanguage,
			renderImage,
			renderLockOverlay,
		]
	);

	/**
	 * Loading and Error States
	 */
	if (loading) {
		return <SplashScreen />;
	}

	if (error) {
		return (
			<View style={styles.container}>
				<Text style={styles.error}>{error}</Text>
			</View>
		);
	}

	return (
		<View style={styles.container}>
			<View style={styles.headerContainer}>
				<View style={styles.headerLeft}>
					<Image
						source={require("../assets/images/logo_HandballTV_header.png")}
						style={styles.logo}
						resizeMode="contain"
					/>
					<TouchableOpacity
						style={[
							styles.authButton,
							isAuthButtonFocused && styles.authButtonFocused,
						]}
						onPress={toggleAuth}
						activeOpacity={1}
						onFocus={() => setIsAuthButtonFocused(true)}
						onBlur={() => setIsAuthButtonFocused(false)}
					>
						<Text
							style={[
								styles.authButtonText,
								isAuthButtonFocused && styles.authButtonTextFocused,
							]}
						>
							{isAuthenticated ? "Disable Auth" : "Enable Auth"}
						</Text>
					</TouchableOpacity>
				</View>
				<View style={styles.headerRight}>
					<LanguageSelector
						currentLanguage={currentLanguage}
						onLanguageChange={handleLanguageChange}
					/>
				</View>
			</View>

			{/* Render the sections */}
			<FlatList
				data={homePageData}
				renderItem={({ item, index }) => {
					// Check for both spellings of "categories" with and without accent
					if (
						item.title?.toLowerCase().includes("catégories") ||
						item.title?.toLowerCase().includes("categories")
					) {
						return (
							<React.Fragment
								key={`section-${item._kenticoId}-${index}`}
							>
								{renderVideoSection({ item })}
								<View style={styles.bannerWrapper}>
									{adSection && renderAdBanner(adSection)}
								</View>
							</React.Fragment>
						);
					}

					// Check if this is the news section
					if (
						item.title?.toLowerCase().includes("a la une") ||
						item.title?.toLowerCase().includes("à la une") ||
						item.title?.toLowerCase().includes("in the news")
					) {
						return (
							<React.Fragment
								key={`section-${item._kenticoId}-${index}`}
							>
								{renderVideoSection({ item })}
							</React.Fragment>
						);
					}

					// Render competitions section with live events section right after it
					if ((item as any).isCompetitionsSection) {
						// Get the live section title
						const liveSectionTitle =
							response?.data?.components?.find(
								(component: any) =>
									component._kenticoCodename ===
									"page_home___lives_a_venir"
							)?.title || "Coming Lives";

						return (
							<React.Fragment key={`section-competitions-${index}`}>
								{/* Render competitions section */}
								{renderVideoSection({ item })}

								{/* Render live and upcoming events section right after competitions */}
								<View style={styles.section}>
									<View style={styles.sectionHeader}>
										<Text style={styles.sectionTitle}>
											{liveSectionTitle}
										</Text>
									</View>
									{Array.isArray(getComingLives) &&
									getComingLives.length === 0 ? (
										<Text style={styles.noEventsText}>
											No upcoming events at the moment
										</Text>
									) : (
										<FlatList
											data={getComingLives}
											horizontal
											showsHorizontalScrollIndicator={false}
											renderItem={renderComingLiveItem}
											keyExtractor={(item) => {
												return (
													item.event?.itemId || (item as any).itemId
												);
											}}
											removeClippedSubviews={true}
											maxToRenderPerBatch={LIST_CONFIG.BATCH_SIZE}
											windowSize={LIST_CONFIG.WINDOW_SIZE}
											initialNumToRender={LIST_CONFIG.INITIAL_RENDER}
											getItemLayout={getLiveItemLayout}
										/>
									)}
								</View>
							</React.Fragment>
						);
					}

					return (
						<React.Fragment
							key={`section-${item._kenticoId}-${index}`}
						>
							{renderVideoSection({ item })}
						</React.Fragment>
					);
				}}
				keyExtractor={(item) => {
					return item._kenticoId || item._kenticoCodename;
				}}
				showsVerticalScrollIndicator={false}
				contentContainerStyle={{
					paddingBottom: 60,
				}}
				removeClippedSubviews={true}
				maxToRenderPerBatch={LIST_CONFIG.BATCH_SIZE}
				windowSize={LIST_CONFIG.WINDOW_SIZE}
				initialNumToRender={LIST_CONFIG.INITIAL_RENDER}
				getItemLayout={getVideoItemLayout}
			/>
		</View>
	);
};
export default React.memo(FetchHomePage);

/**
 * Common Use Cases:
 * - Responsive sizing: scale(value) - Dynamically adjusts dimensions based on screen size
 * - Toggle visibility: opacity === 0 || styles.hidden
 * - Apply focus states: isFocused && styles.focused
 * - Apply authentication states: isAuthenticated && styles.premium
 * - Apply live states: isLive && styles.liveItem
 * - Apply hover effects: isHovered && styles.hovered (TV navigation)
 * - Apply conditional styles: inTheNewsCat && styles.inTheNewsVideoItem
 * - Conditional styles: inTheNewsCat && styles.inTheNewsVideoItem
 * - Focus states: isAuthButtonFocused && styles.authButtonFocused
 * - Live/Upcoming states: item.isLive && styles.liveItem
 */
const styles = StyleSheet.create({
	/**
	 * Core Layout Styles
	 * These styles define the main container and section layouts
	 * Uses flex layout for responsive design across different screen sizes
	 */
	container: {
		flex: 1, // Makes the container fill the entire screen vertically
		backgroundColor: GLOBAL_STYLES.COLORS.PAGE_BACKGROUND, // Uses global background color
		paddingTop: 12, // Adds spacing at the top to prevent content from touching the screen edge
		paddingHorizontal: GLOBAL_STYLES.PAGE_HORIZONTAL_PADDING, // Uses global horizontal padding
	},
	section: {
		marginBottom: 24, // Adds vertical spacing between different content sections for visual separation
	},
	sectionHeader: {
		flexDirection: "row", // Arranges the title and any controls horizontally
		justifyContent: "space-between", // Places maximum space between the title and optional controls
		alignItems: "center", // Centers items vertically within the header
		marginBottom: 8, // Adds space between the header and its content below
	},
	bannerWrapper: {
		marginTop: 12, // Adds space above the advertisement banner
		marginBottom: -20, // Negative margin pulls the next section closer, compensating for section spacing
	},

	/**
	 * Header Component Styles
	 * Defines the app's top navigation bar layout and appearance
	 * Includes logo, auth button, and language selector positioning
	 */
	headerContainer: {
		flexDirection: "row", // Arranges logo, auth button, and language selector horizontally
		justifyContent: "space-between", // Maximizes space between left (logo) and right (controls) sections
		alignItems: "center", // Centers all header elements vertically
		paddingVertical: 10, // Adds vertical padding for header height
		paddingHorizontal: 20, // Adds horizontal padding for content spacing
		backgroundColor: GLOBAL_STYLES.COLORS.PAGE_BACKGROUND, // Uses global background color
		marginHorizontal: -20, // Extends the header beyond the main content padding
	},
	headerLeft: {
		flexDirection: "row", // Arranges logo and auth button horizontally
		alignItems: "center", // Centers the logo and auth button vertically
		gap: 16, // Adds consistent spacing between logo and auth button
	},
	headerRight: {
		flexDirection: "row", // Arranges language selector items horizontally
		alignItems: "center", // Centers language selector items vertically
	},
	logo: {
		width: 180, // Sets a fixed width for the logo to maintain consistent branding
	},

	/**
	 * Authentication Button Styles
	 * Interactive button with focus states for TV navigation
	 * Includes hover and focus animations
	 */
	authButton: {
		padding: 8, // Adds internal spacing for the button content
		backgroundColor: "#102e55", // Matches the background for a subtle appearance
		borderRadius: 5, // Rounds the button corners for a modern look
		borderWidth: 2, // Adds a visible border to make the button stand out
		borderColor: "#2A5A99", // Uses brand blue for the border
		transform: [{ scale: 1.0 }], // Base scale for focus animation
	},
	authButtonFocused: {
		backgroundColor: "#2A5A99", // Changes background when focused for visual feedback
		borderColor: "#f9f9f9", // Changes to white border for contrast when focused
		transform: [{ scale: 1.1 }], // Slightly enlarges the button when focused
	},
	authButtonText: {
		color: "#f9f9f9", // Sets light text color for contrast
		fontSize: scale(24), // Sets font size based on platform
	},
	authButtonTextFocused: {
		color: "#ffffff", // Brightens text color when focused
		fontWeight: "bold", // Makes text bolder when focused for emphasis
	},

	/**
	 * Video Item Card Styles
	 * Defines the appearance of video cards in the carousel
	 * Includes responsive sizing for different screens
	 */
	videoItem: {
		width: scale(360), // Base width
		marginRight: scale(32),
		opacity: 0.7,
	},
	thumbnailContainer: {
		position: "relative",
		width: "100%",
		height: scale(203), // Updated to maintain 16:9 ratio (360 * 9/16 ≈ 203)
		backgroundColor: "#000",
		overflow: "hidden",
		borderRadius: 6, // Adding borderRadius for consistency
	},
	thumbnail: {
		width: "100%",
		height: "100%", // Fill container completely
		backgroundColor: "#333",
	},

	/**
	 * Coming Live Event Card Styles
	 * Special styling for upcoming live event cards
	 * Includes larger dimensions and distinct visual treatment
	 */
	comingLiveVideoItem: {
		width: scale(432), // Wider cards for live events
		marginRight: scale(36), // Proportional spacing
		opacity: 0.7, // Matches regular card opacity for consistency
	},
	comingLiveThumbnailContainer: {
		position: "relative", // Enables overlay positioning
		width: "100%", // Takes full width of parent
		height: scale(244), // Taller aspect ratio for live events
		backgroundColor: "#000", // Dark background for loading
		borderRadius: 10, // Rounded corners for live event cards
		overflow: "hidden", // Clips content to rounded corners
	},
	comingLiveThumbnail: {
		width: "100%", // Fills container width
		height: scale(244), // Matches container height
		backgroundColor: "#333", // Placeholder color while loading
	},
	belowImageContent: {
		paddingTop: 12, // Spaces content between image and text
		paddingHorizontal: 4, // Adds side padding for text alignment from edges
	},

	/**
	 * Competition Card Styles
	 * Styling for competition cards in the competitions section
	 * Includes distinct visual treatment for competition items
	 */
	competitionSection: {
		marginTop: scale(16), // Add top margin to the competition section
		marginBottom: scale(24), // Add bottom margin to the competition section
		paddingVertical: scale(12), // Add vertical padding inside the section
	},
	competitionItem: {
		width: scale(280), // Fixed width for competition cards
		marginRight: 0, // Remove right margin as we're using ItemSeparatorComponent
		marginBottom: scale(8), // Reduced bottom margin for more compact layout
		opacity: 0.7, // Slightly transparent when not focused
	},
	competitionImageContainer: {
		width: "100%", // Takes full width of parent
		height: scale(150), // Reduced height to better match the reference image
		borderRadius: scale(8), // Rounded corners for competition cards
		overflow: "hidden", // Clips content to rounded corners
		backgroundColor: "#1a3c6b", // Background color for empty state
	},
	competitionImage: {
		width: "100%", // Fills container width
		height: "100%", // Fills container height
		// No resizeMode here as it's set directly on the Image component
		backgroundColor: "#1a3c6b", // Background color to match container
	},
	competitionTitle: {
		fontSize: scale(22), // Increased font size for competition titles
		color: "#ffffff", // White text for contrast
		fontWeight: "bold", // Bold font weight for emphasis
		marginTop: scale(4), // Reduced space between image and title
		textAlign: "left", // Left-aligned text as requested
	},

	/**
	 * Featured "In the News" Card Styles
	 * Largest card size with special styling for featured content
	 * Includes enhanced typography and larger images
	 */
	inTheNewsVideoItem: {
		width: scale(600), // Largest card size for featured content
		marginRight: scale(28), // Proportional spacing
		opacity: 0.7, // Consistent with other cards
	},
	inTheNewsThumbnailContainer: {
		height: scale(337), // Larger images for featured content
		width: "100%", // Full width of parent
		backgroundColor: "#000", // Dark loading background
		overflow: "hidden", // Clips content to container
	},
	inTheNewsThumbnail: {
		height: scale(337), // Matches container height
		width: "100%", // Full width for proper scaling
	},
	inTheNewsVideoTitle: {
		fontSize: scale(24), // Larger text for featured content
		color: "#f9f9f9", // Light text for contrast
		marginTop: 8, // Spaces title from thumbnail
	},

	/**
	 * Event Information Overlay Styles
	 * Semi-transparent overlay for displaying event details
	 * Includes date/time information and status indicators
	 */
	eventInfoOverlay: {
		position: "absolute", // Positions the overlay absolutely within its parent
		left: 0, // Aligns to the left
		right: 0, // Aligns to the right
		bottom: 0, // Aligns to the bottom
		padding: scale(4), // Adds padding
		backgroundColor: "rgba(0, 0, 0, 0.7)", // Sets a semi-transparent black background
	},
	eventTimeRow: {
		flexDirection: "row", // Arranges time elements in a row
		justifyContent: "space-between", // Distributes space between items
		alignItems: "center", // Centers items vertically
		width: "100%", // Takes full width of the parent
		marginBottom: scale(4), // Adds bottom margin
	},
	timeContainer: {
		flexDirection: "row", // Arranges time elements in a row
		alignItems: "center", // Centers items vertically
		gap: 4, // Adds space between items
	},
	clockIcon: {
		fontSize: scale(20), // Sets font size
		marginRight: 4, // Adds space to the right of the clock icon
	},
	eventTimeText: {
		fontSize: scale(24), // Sets font size
		color: "#f9f9f9", // Sets text color to light for contrast
		fontWeight: "500", // Sets font weight to medium
	},
	liveIndicator: {
		fontSize: scale(18),
		fontWeight: "bold",
		color: "#FF0000", // Bright red color for live videos
		textTransform: "uppercase",
	},
	upcomingIndicator: {
		fontSize: scale(18), // Sets font size
		fontWeight: "bold", // Makes the text bold
		color: "#ffa500", // Sets text color to orange
		textTransform: "uppercase", // Transforms text to uppercase
		margin: 0, // Removes margin to eliminate extra space
	},

	/**
	 * Premium Content Lock Overlay Styles
	 * Visual indicator for content requiring authentication
	 * Includes lock icon and red tint overlay for non-authenticated users
	 * And small lock icon in top-right for authenticated users
	 */
	lockOverlay: {
		position: "absolute", // Positions over thumbnail
		top: 0, // Aligns to top
		left: 0, // Aligns to left
		right: 0, // Stretches to right
		bottom: 0, // Stretches to bottom
		borderRadius: 8, // Rounds corners to match container
		justifyContent: "center", // Centers lock icon vertically
		alignItems: "center", // Centers lock icon horizontally
		backgroundColor: "rgba(255, 0, 0, 0.2)", // Subtle red tint for premium content
	},
	lockIconWrapper: {
		position: "absolute",
		top: scale(16),
		right: scale(16),
	},
	lockIconContainer: {
		width: scale(48),
		height: scale(48),
		borderRadius: scale(24),
		backgroundColor: "rgba(0, 0, 0, 0.7)",
		justifyContent: "center",
		alignItems: "center",
	},
	lockIcon: {
		fontSize: scale(28),
	},
	lockIconContainerLarge: {
		width: scale(80),
		height: scale(80),
		borderRadius: scale(40),
		backgroundColor: "rgba(0, 0, 0, 0.5)",
		justifyContent: "center",
		alignItems: "center",
	},
	lockIconLarge: {
		fontSize: scale(56),
	},

	/**
	 * Typography Styles
	 * Defines text styles across the application
	 * Includes responsive sizing and consistent color scheme
	 */
	sectionTitle: {
		fontSize: scale(38), // Large size for section headers
		fontWeight: "bold", // Makes headers stand out
		color: "#f9f9f9", // Light text for contrast
		marginBottom: 8, // Spaces title from content
	},
	videoTitle: {
		fontSize: scale(25), // Readable size for titles
		color: "#f9f9f9", // Light text for contrast
		marginBottom: 4, // Spaces from description
	},
	eventDay: {
		fontSize: scale(25), // Appropriate size for date
		color: "#f9f9f9", // Light text for contrast
		fontWeight: "600", // Semi-bold for emphasis
	},
	eventTime: {
		fontSize: scale(25), // Matches date size
		color: "#f9f9f9", // Light text for contrast
	},
	eventDescription: {
		fontSize: scale(21), // Smaller text for descriptions
		color: "#f9f9f9", // Light text for contrast
		opacity: 0.8, // Slightly dimmed for hierarchy
		marginTop: 4, // Spaces from title
	},
	error: {
		color: "red", // Error text in red
		textAlign: "center", // Centers error message
		fontSize: scale(24), // Visible error size
	},
	noEventsText: {
		fontSize: scale(21), // Readable message size
		color: "#f9f9f9", // Light text for contrast
		textAlign: "center", // Centers message
		marginTop: 10, // Spaces from header
	},

	/**
	 * Live Item State Styles
	 * Special styling for items in live state
	 * Includes red border and subtle background highlight
	 */
	liveItem: {
		borderColor: "#ff0000", // Red border for live content
		borderWidth: 1, // Visible border width
		borderRadius: 8, // Rounded corners
		padding: 4, // Internal spacing
		backgroundColor: "rgba(44, 39, 39, 0.12)", // Subtle dark highlight
	},

	/**
	 * Duration Label Styles
	 * Displays the video duration overlay on thumbnails
	 */
	durationContainer: {
		position: "absolute",
		bottom: scale(16),
		right: scale(16),
		backgroundColor: "rgba(0, 0, 0, 0.7)",
		paddingHorizontal: scale(12),
		paddingVertical: scale(4),
		borderRadius: scale(8),
	},
	durationText: {
		color: "#FFFFFF",
		fontSize: scale(24),
		fontWeight: "500",
	},
});
