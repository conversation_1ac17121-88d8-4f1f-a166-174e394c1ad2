# OTT_TV

TV versions (Android TV, Apple TV, WebOS, Tizen, Hisense etc.) for OTT Platform

This project uses ReNative Framework.
version 1.3.0

### Requirements

[Node](https://nodejs.org/en) 18.19.1 or newer

### First time?

Install ReNative CLI globally:

```bash
npm install rnv -g
```

Specific version:

```bash
npm install rnv@1.3.0 -g
```

#### Using NPX

After the initial setup, it is recommended to use npx for running ReNative commands.

To use npx, ensure it is installed globally:

```bash
npm install npx -g
```

## Get started

1.  Install dependencies

    ```bash
    npm install @rnv/renative@1.3.0 --save-dev

    npm install
    ```

- Version Check

  To ensure correct version:

  ```bash
  npx rnv -v
  ```

  If you see version mismatch:

  ```bash
  npm uninstall -g rnv

  rm -rf node_modules
  rm package-lock.json

  npm install @rnv/renative@1.3.0 --save-dev
  npm install
  ```

2.  Start the app

    ```bash
     npx rnv run
    ```

    And choose platform (e.g. tvos, androidtv)

3.  Problem starting?

    ```bash
    npm cache clean --force
    rm -rf node_modules
    rm package-lock.json
    npm install
    ```

    ```bash
    npx rnv run --reset-cache
    ```

    ```bash
    npx rnv run -p <platform> --reset-cache
    ```

Replace <platform> with your target platform (like androidtv, tvos, oweb).

###### If this does not resolve the issue, consider running platform-specific rebuild:

```bash
npx rnv run --reset
```

```bash
npx rnv run -p <platform> --reset
```

1.  Update (if needed)

    ```bash
    rm -rf node_modules package-lock.json
    ```

    ```bash
    npm install react-native@latest
    ```

    ```bash
    npm install rnv@latest
    ```

    ```bash
    npx rnv configure
    ```

    ```bash
    npx rnv project configure
    ```

##### To check app health

```bash
npx react-native doctor
```

### Dependencies for TV Platforms

To run applications for TV platforms (such as Apple TV or Android TV), you need to install additional dependencies specific to those platforms:

1. For **Apple TV**:
   Ensure you have Xcode installed and configured properly for
   [tvOS Platform Documentation](https://www.renative.org/docs/platforms/tvos)

   - Latest swift based Xcode project
   - Cocoapods Workspace ready
   - Swift 4.1 Support

##### Requirements

- [Xcode](https://developer.apple.com/xcode/) for iOS/tvOS
- [CocoaPods](https://cocoapods.org/) 1.5.3 or newer

##### Run on Simulator

```bash
npx rnv run -p tvos
```

2. For **Android TV**:
   Make sure you have the Android SDK installed and configured in your environment.
   [Android TV Platform Documentation](https://www.renative.org/docs/platforms/androidtv)

- Latest Android project
- Kotlin Support
- Support for Gradle 4.9

##### Requirements

- [Android Studio](https://developer.android.com/studio) for Android
- Android SDK 23.0.1 or newer

##### Run on Simulator

```bash
npx rnv run -p androidtv
```

Target emulator: Television_720p_API_34 | TV 📺 | arch: x86

3. **WebOS Platform**:

   [WebOS documentation](https://www.renative.org/docs/platforms/webos)

   ##### Requirements

   - [LG Emulator](https://webostv.developer.lge.com/develop/tools/emulator-introduction) v3.0.0

4. **Tizen TV**:

   [Tizen TV documentation](https://www.renative.org/docs/platforms/tizen)

   ##### Requirements

   - [Tizen SDK](https://developer.tizen.org/development/tizen-studio/configurable-sdk?langredirect=1) 5.0
   - Make sure your CPU supports virtualization. Otherwise Tizen emulator might not start.
   - If you are deploying to a TV, follow this guide to set your TV in developer mode [guide](https://developer.samsung.com/smarttv/develop/getting-started/using-sdk/tv-device.html)

##### Installing Plugins

Installation example:

```bash
rnv plugin add react-native-video
```

##### Linking plugins

ReNative usually handles linking automatically. However, if you encounter issues, you might need to manually link the plugin.

```bash
npx react-native link react-native-video
```

##### App Structure example

```plaintext
root/
├── public/                                  # Static files (e.g., fonts, images)
│   ├── images/
│   └── thumbnails/
├── src/                                     # Source files for the app
│   ├── app/                                 # Main app entry point and setup files
│   │   └── index.tsx                        # Main entry point for the application
│   ├── assets/                              # Application assets
│   │   └── images/                          # Image assets including splash screens and logos
│   ├── components/                          # Reusable UI components
│   │   ├── homePageComponents/              # Components specific to the home page
│   │   │   ├── AdBanner.tsx                 # Advertisement banner component
│   │   │   ├── LanguageSelector.tsx         # Language selection component
│   │   │   └── SplashScreen.tsx             # Initial loading screen
│   │   ├── videoDetailsComponents/          # Components for video details
│   │   │   ├── VideoDescription.tsx
│   │   │   ├── RelatedVideos.tsx
│   │   │   ├── VideoDetailsPlayButton.tsx
│   │   │   └── UpcomingLiveModalTimer.tsx
│   │   └── ...
│   ├── navigation/                          # Navigation setup
│   │   └── AppNavigator.tsx
│   ├── pages/                               # Screen components (views)
│   │   ├── HomePage.tsx                     # Home screen with video grid and live events
│   │   ├── CategoriesPage.tsx               # Categories listing and navigation screen
│   │   ├── VideoDetailsPage.tsx             # Video details and player screen
│   │   └── ...
│   ├── utils/                               # Utility files (e.g., API calls, helpers)
│   │   ├── apis/                            # API-related code
│   │   │   └── generated/                   # Generated API clients
│   │   ├── helpers/                         # Helper functions
│   │   │   └── dimensionScale.helper.ts     # UI scaling utility for TV resolutions
│   │   └── kenticoHelpers.ts                # Kentico-specific utilities
│   ├── types/                               # TypeScript type definitions
│   │   └── kentico.ts                       # Kentico-specific types
│   └── config/                              # Configuration files
│       └── auth.ts                          # Authentication configuration
├── node_modules/                            # Installed node modules
├── package.json                             # NPM configuration
├── tsconfig.json                            # TypeScript configuration
└── ...
```

##### UI Scaling Utility

The app includes a resolution-independent scaling system (`src/utils/helpers/dimensionScale.helper.ts`) that dynamically adjusts UI elements based on screen size. Instead of using platform-specific dimensions:

```typescript
// Instead of platform-specific hardcoded values:
fontSize: Platform.OS === "android" ? 14 : 22,

// Use the scale utility:
import { scale } from "../../utils/helpers/dimensionScale.helper";
fontSize: scale(22), // Automatically scales for any TV resolution
```

This ensures consistent UI appearance across different TV devices and screen resolutions.
